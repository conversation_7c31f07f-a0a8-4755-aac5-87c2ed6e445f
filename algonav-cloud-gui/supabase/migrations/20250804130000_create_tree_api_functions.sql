-- Migration: Create RPC functions for new Tree, Var-Nodes, and Job Effective APIs
-- Date: 2025-08-04
-- Description: Adds RPC functions to support the new API endpoints for tree navigation,
--              variable node discovery, and job variable merging with proper hierarchy

-- =====================================================
-- 1. Tree Skeleton Function
-- =====================================================

-- Function to get category tree skeleton with optional dataset counts
CREATE OR REPLACE FUNCTION get_tree_skeleton(with_counts boolean DEFAULT true)
RETURNS TABLE (
    id integer,
    "parentId" integer,
    name varchar,
    path text,
    level integer,
    "datasetCnt" bigint
)
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    IF with_counts THEN
        RETURN QUERY
        SELECT 
            c.id,
            c.parent_category_id as "parentId",
            c.name,
            c.path::text,
            nlevel(c.path) as level,
            COALESCE(COUNT(d.id), 0) as "datasetCnt"
        FROM categories c
        LEFT JOIN datasets d ON d.category_id = c.id
        GROUP BY c.id, c.parent_category_id, c.name, c.path
        ORDER BY c.path;
    ELSE
        RETURN QUERY
        SELECT 
            c.id,
            c.parent_category_id as "parentId",
            c.name,
            c.path::text,
            nlevel(c.path) as level,
            0::bigint as "datasetCnt"
        FROM categories c
        ORDER BY c.path;
    END IF;
END; $$;

-- =====================================================
-- 2. Variable Node Discovery Functions
-- =====================================================

-- Function to find categories that contain a specific variable key
-- Handles both direct key-value structure and vars array structure
CREATE OR REPLACE FUNCTION get_categories_with_key(p_key text, p_user_id uuid)
RETURNS TABLE (id int, path text)
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.path::text
    FROM categories c
    WHERE c.user_id = p_user_id 
      AND (
        -- Check direct key-value structure
        c.variable_overrides ? p_key
        OR
        -- Check vars array structure
        EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      );
END; $$;

-- Function to find datasets that contain a specific variable key
-- Handles both direct key-value structure and vars array structure
CREATE OR REPLACE FUNCTION get_datasets_with_key(p_key text, p_user_id uuid)
RETURNS TABLE (id int, path text)
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT d.id, d.path::text
    FROM datasets d
    WHERE d.user_id = p_user_id 
      AND d.path IS NOT NULL  -- Only include datasets with paths
      AND (
        -- Check direct key-value structure
        d.variable_overrides ? p_key
        OR
        -- Check vars array structure
        EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      );
END; $$;

-- Function to find templates that contain a specific variable key
-- Searches in vars array structure of global_job_templates
CREATE OR REPLACE FUNCTION get_templates_with_key(p_key text)
RETURNS TABLE (id int, path text)
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT jt.id, 'template' AS path
    FROM global_job_templates jt
    WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
        WHERE var_item->>'name' = p_key
    );
END; $$;

-- Enhanced var-nodes function that marks which node has the active variable definition
-- This requires a dataset context to determine the merge hierarchy
CREATE OR REPLACE FUNCTION get_var_nodes_with_active_status(p_key text, p_user_id uuid, p_dataset_id int DEFAULT NULL)
RETURNS TABLE (
    id int,
    path text,
    kind text,
    is_active boolean
)
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    dataset_path ltree;
    active_source_type text;
    active_source_id int;
BEGIN
    -- If dataset_id is provided, determine which source has the active variable
    IF p_dataset_id IS NOT NULL THEN
        SELECT d.path INTO dataset_path
        FROM datasets d
        WHERE d.id = p_dataset_id AND d.user_id = p_user_id;

        IF dataset_path IS NULL THEN
            RAISE EXCEPTION 'Dataset not found or access denied';
        END IF;

        -- Check dataset first (highest priority)
        IF EXISTS (
            SELECT 1 FROM datasets d
            WHERE d.id = p_dataset_id
              AND d.user_id = p_user_id
              AND (
                d.variable_overrides ? p_key
                OR EXISTS (
                  SELECT 1
                  FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
                  WHERE var_item->>'name' = p_key
                )
              )
        ) THEN
            active_source_type := 'dataset';
            active_source_id := p_dataset_id;
        ELSE
            -- Check categories (by priority level - deepest wins)
            SELECT c.id INTO active_source_id
            FROM categories c
            WHERE c.path @> dataset_path
              AND c.user_id = p_user_id
              AND (
                c.variable_overrides ? p_key
                OR EXISTS (
                  SELECT 1
                  FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
                  WHERE var_item->>'name' = p_key
                )
              )
            ORDER BY nlevel(c.path) DESC
            LIMIT 1;

            IF active_source_id IS NOT NULL THEN
                active_source_type := 'category';
            ELSE
                -- Check templates (lowest priority)
                SELECT jt.id INTO active_source_id
                FROM global_job_templates jt
                WHERE EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements(jt.vars->'vars') AS var_item
                    WHERE var_item->>'name' = p_key
                )
                LIMIT 1; -- For now, just pick the first template

                IF active_source_id IS NOT NULL THEN
                    active_source_type := 'template';
                END IF;
            END IF;
        END IF;
    END IF;

    -- Return all nodes with active status
    RETURN QUERY
    -- Categories
    SELECT
        c.id,
        c.path::text,
        'cat'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'category' AND c.id = active_source_id) as is_active
    FROM categories c
    WHERE c.user_id = p_user_id
      AND (
        c.variable_overrides ? p_key
        OR EXISTS (
          SELECT 1
          FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      )

    UNION ALL

    -- Datasets
    SELECT
        d.id,
        d.path::text,
        'ds'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'dataset' AND d.id = active_source_id) as is_active
    FROM datasets d
    WHERE d.user_id = p_user_id
      AND d.path IS NOT NULL
      AND (
        d.variable_overrides ? p_key
        OR EXISTS (
          SELECT 1
          FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      )

    UNION ALL

    -- Templates
    SELECT
        jt.id,
        'template'::text,
        'tpl'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'template' AND jt.id = active_source_id) as is_active
    FROM global_job_templates jt
    WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
        WHERE var_item->>'name' = p_key
    );
END; $$;

-- =====================================================
-- 3. Job Variable Merging Function
-- =====================================================

-- Function to merge job variables from template, category hierarchy, and datasets
-- Handles complex variable structures with name/data/links format and gui objects
-- Merge hierarchy: Template → Root-Category → Sub-Category → ... → Dataset (with proper deduplication)
-- Variables with same name: Dataset > Sub-Category > Root-Category > Template (deeper categories win)
CREATE OR REPLACE FUNCTION merge_job_vars(p_template_id int, p_dataset_ids int[])
RETURNS TABLE (
    "datasetId" int,
    vars jsonb
)
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    WITH job AS (
        SELECT p_template_id AS template_id,
               UNNEST(p_dataset_ids) AS dataset_id
    ),
    all_vars AS (
        -- Template variables (priority 1000 - lowest)
        SELECT
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            1000 AS priority
        FROM job
        LEFT JOIN global_job_templates jt ON jt.id = job.template_id
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(jt.vars->'vars', '[]'::jsonb)) AS var_item

        UNION ALL

        -- Category variables from vars array (priority 2000 + level - deeper categories win)
        SELECT
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            2000 + nlevel(c.path) AS priority  -- Deeper categories have higher priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        JOIN categories c ON c.path @> d.path
        CROSS JOIN LATERAL jsonb_array_elements(c.variable_overrides->'vars') AS var_item
        WHERE c.variable_overrides ? 'vars'

        UNION ALL

        -- Category variables from simple key-value (priority 2000 + level)
        SELECT
            job.dataset_id,
            kv.key AS var_name,
            jsonb_build_object(
                'name', kv.key,
                'data', kv.value,
                'links', '[]'::jsonb
            ) AS var_object,
            2000 + nlevel(c.path) AS priority  -- Deeper categories have higher priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        JOIN categories c ON c.path @> d.path
        CROSS JOIN LATERAL jsonb_each(c.variable_overrides) AS kv
        WHERE NOT (c.variable_overrides ? 'vars') AND kv.key != 'vars'

        UNION ALL

        -- Dataset variables (priority 9000 - highest)
        SELECT
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            9000 AS priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(d.variable_overrides->'vars', '[]'::jsonb)) AS var_item
    ),
    deduplicated_vars AS (
        SELECT
            dataset_id,
            jsonb_agg(
                var_object
                ORDER BY var_name
            ) AS vars_array
        FROM (
            SELECT DISTINCT ON (dataset_id, var_name)
                dataset_id,
                var_name,
                var_object,
                priority
            FROM all_vars
            WHERE var_name IS NOT NULL
            ORDER BY dataset_id, var_name, priority DESC  -- Higher priority wins
        ) unique_vars
        GROUP BY dataset_id
    )
    SELECT
        dv.dataset_id as "datasetId",
        jsonb_build_object('vars', COALESCE(dv.vars_array, '[]'::jsonb)) AS vars
    FROM deduplicated_vars dv;
END; $$;

-- =====================================================
-- 4. Grant Permissions
-- =====================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_tree_skeleton(boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION get_categories_with_key(text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_datasets_with_key(text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_templates_with_key(text) TO authenticated;
GRANT EXECUTE ON FUNCTION get_var_nodes_with_active_status(text, uuid, int) TO authenticated;
GRANT EXECUTE ON FUNCTION merge_job_vars(int, int[]) TO authenticated;

-- =====================================================
-- 5. Comments for Documentation
-- =====================================================

COMMENT ON FUNCTION get_tree_skeleton(boolean) IS
'Returns hierarchical category tree with optional dataset counts. Used by /api/tree endpoint.';

COMMENT ON FUNCTION get_categories_with_key(text, uuid) IS
'Finds categories containing a specific variable key in either direct or vars array format. Used by /api/var-nodes endpoint.';

COMMENT ON FUNCTION get_datasets_with_key(text, uuid) IS
'Finds datasets containing a specific variable key in either direct or vars array format. Used by /api/var-nodes endpoint.';

COMMENT ON FUNCTION get_templates_with_key(text) IS
'Finds templates containing a specific variable key in vars array format. Used by /api/var-nodes endpoint.';

COMMENT ON FUNCTION get_var_nodes_with_active_status(text, uuid, int) IS
'Enhanced var-nodes function that marks which node has the active variable definition based on merge hierarchy. Used by /api/var-nodes endpoint with datasetId parameter.';

COMMENT ON FUNCTION merge_job_vars(int, int[]) IS
'Merges job variables from template, category hierarchy, and datasets with proper precedence. Used by /api/job/effective endpoint. Handles complex variable structures with name/data/links format and gui objects.';
